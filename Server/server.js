import express from 'express';

import { createPrinter } from 'typescript';
import printer from 'printer';

const app = express();
const port = 3000;

app.get('/', (req, res) => {
   res.send('Hello World!')
})

app.post('/generate-pdf', async (req, res) => {
   const { html } = req.body;
   try {
      const browser = await puppeteer.launch();
      const page = await browser.newPage();
      await page.setContent(html, { webkitURL: "networkidle0" });

      const pdfPath = path.join(__dirname, 'invoice.pdf');
      await page.pdf({ path: pdfPath, format: 'A4', printBackground: true });
      await browser.close();

      const fileBuffer = fs.readFileSync(pdfPath);
      printer.printDirect({
         data: fileBuffer,
         type: 'PDF',
         success: (jobID) => {
            console.log("sent to printer with ID: " + jobID);
         },
         error: (err) => {
            console.log(err);
         }
      })

      res.send("Invoice generated and sent to printer");

   } catch (error) {
      res.status(500).send("Error:",error.message);
   }
})

app.listen(port, () => {
   console.log(`Example app listening on port ${port}`)
})
